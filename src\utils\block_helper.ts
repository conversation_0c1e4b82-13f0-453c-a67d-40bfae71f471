import { blockBodyOptions } from 'src/data/blocks';
import type { BlockBodyOptionsType } from 'src/types/app';
import type { ItemBlock, Option } from 'src/types/models';

export const extractBlockBodyType = (itemBlock: ItemBlock): BlockBodyOptionsType => {
  return blockBodyOptions.find((option) => option.value === itemBlock.type) || blockBodyOptions[0]!;
};

/**
 * Helper function to determine the correct section for a new block
 * @param blocks - Array of ItemBlocks to search through
 * @param index - Index position to determine section for
 * @returns Section number for the new block
 */
export const getCurrentSection = (blocks: ItemBlock[], index: number): number => {
  // Find the section of the block at the given index
  const currentBlock = blocks[index];
  if (currentBlock) {
    return currentBlock.section;
  }

  // If no current block, find the section of the nearest previous block
  for (let i = index - 1; i >= 0; i--) {
    const block = blocks[i];
    if (block) {
      return block.section;
    }
  }

  // Default to section 1 if no blocks found
  return 1;
};

/**
 * Helper function to copy content from source block to duplicated block with proper API calls
 *
 * FIXED: This function now properly handles ItemBlock duplication by:
 * 1. Waiting for backend to finish creating default options (prevents race conditions)
 * 2. Completely removing all existing default options before creating new ones
 * 3. Creating exact copies of source options with proper backend persistence
 * 4. Verifying the final option count matches the source
 * 5. Detecting and logging any duplicate option IDs
 *
 * @param source - Source ItemBlock to copy from
 * @param target - Target ItemBlock to copy to (with backend-generated defaults)
 * @returns Promise<ItemBlock> - Updated target block with copied content
 */
export const copyBlockContentWithBackendPersistence = async (
  source: ItemBlock,
  target: ItemBlock,
): Promise<ItemBlock> => {
  console.log('📋 Starting content copy process with backend persistence...', {
    sourceType: source.type,
    sourceId: source.id,
    targetId: target.id,
    sourceOptions: source.options?.length || 0,
    targetOptions: target.options?.length || 0,
  });

  try {
    // Import OptionService for creating options with backend persistence
    const { OptionService } = await import('src/services/asm/optionService');
    const optionService = new OptionService();

    // Wait a moment to ensure backend has finished creating default options
    // This prevents race conditions where default options are created after we start
    await new Promise((resolve) => setTimeout(resolve, 200));

    // Create a deep copy of the target block to avoid mutations
    const updatedTarget = { ...target };

    // Copy and update questions if source has questions
    if (source.questions && source.questions.length > 0 && updatedTarget.questions) {
      console.log(
        '📝 Copying and updating questions with backend persistence...',
        source.questions.length,
      );

      const updatedQuestions = [];

      // Update existing questions with source content via backend API
      for (let index = 0; index < updatedTarget.questions.length; index++) {
        const targetQuestion = updatedTarget.questions[index];
        const sourceQuestion = source.questions[index];

        if (sourceQuestion && targetQuestion) {
          try {
            // Import QuestionService dynamically
            const questionServiceModule = await import('src/services/asm/questionService');
            const questionService = questionServiceModule.default;

            // Prepare question update data
            const questionUpdateData = {
              questionText: sourceQuestion.questionText || '', // ✅ Preserve original questionText
              isHeader: sourceQuestion.isHeader,
              sequence: sourceQuestion.sequence,
              score: sourceQuestion.score || 0,
              itemBlockId: target.id,
              // Copy optional properties if they exist
              ...(sourceQuestion.imagePath && { imagePath: sourceQuestion.imagePath }),
              ...(sourceQuestion.imageWidth && { imageWidth: sourceQuestion.imageWidth }),
              ...(sourceQuestion.imageHeight && { imageHeight: sourceQuestion.imageHeight }),
              ...(sourceQuestion.sizeLimit && { sizeLimit: sourceQuestion.sizeLimit }),
              ...(sourceQuestion.acceptFile && { acceptFile: sourceQuestion.acceptFile }),
              ...(sourceQuestion.uploadLimit && { uploadLimit: sourceQuestion.uploadLimit }),
            };

            console.log(
              `🌐 Updating question ${targetQuestion.id} via API with data:`,
              questionUpdateData,
            );
            const updatedQuestion = await questionService.updateQuestion(
              targetQuestion.id,
              questionUpdateData,
            );

            if (updatedQuestion) {
              updatedQuestions.push(updatedQuestion);
              console.log(`✅ Updated question with backend persistence ${updatedQuestion.id}:`, {
                id: updatedQuestion.id,
                questionText: updatedQuestion.questionText, // ✅ This shows the persisted questionText
                isHeader: updatedQuestion.isHeader,
                sequence: updatedQuestion.sequence,
              });
            }
          } catch (error) {
            console.warn(`⚠️ Failed to update question ${targetQuestion.id}:`, error);
            // Fallback to original question if update fails
            updatedQuestions.push(targetQuestion);
          }
        } else if (targetQuestion) {
          // Keep original question if no corresponding source question
          updatedQuestions.push(targetQuestion);
        }
      }

      // Update the target with the backend-updated questions
      if (updatedQuestions.length > 0) {
        updatedTarget.questions = updatedQuestions;
        console.log(
          `✅ Successfully updated ${updatedQuestions.length} questions with backend persistence`,
          {
            updatedQuestionsData: updatedQuestions.map((q) => ({
              id: q.id,
              questionText: q.questionText, // ✅ These are backend-persisted questionText values
              isHeader: q.isHeader,
              sequence: q.sequence,
            })),
          },
        );
      }
    }

    // Copy and create options if source has options
    if (source.options && source.options.length > 0) {
      console.log('🔘 Copying and creating options with backend persistence...', {
        sourceOptionsCount: source.options.length,
        targetOptionsCount: target.options?.length || 0,
        sourceOptionsData: source.options.map((opt) => ({
          id: opt.id,
          optionText: opt.optionText,
          value: opt.value,
          sequence: opt.sequence,
        })),
        targetOptionsData:
          target.options?.map((opt) => ({
            id: opt.id,
            optionText: opt.optionText,
            value: opt.value,
            sequence: opt.sequence,
          })) || [],
      });

      const createdOptions: Option[] = [];

      // First, ensure we have a clean slate by removing ALL existing options
      if (target.options && target.options.length > 0) {
        console.log(`🗑️ Removing ${target.options.length} existing default options...`);

        // Create array of deletion promises to handle them concurrently but safely
        const deletionPromises = target.options.map(async (existingOption) => {
          try {
            console.log(`🗑️ Deleting option ${existingOption.id}...`);
            await optionService.removeOption(existingOption.id);
            console.log(`✅ Successfully deleted option ${existingOption.id}`);
            return { success: true, id: existingOption.id };
          } catch (error) {
            console.error(`❌ Failed to delete option ${existingOption.id}:`, error);
            return { success: false, id: existingOption.id, error };
          }
        });

        // Wait for all deletions to complete
        const deletionResults = await Promise.all(deletionPromises);
        const successfulDeletions = deletionResults.filter((result) => result.success);
        const failedDeletions = deletionResults.filter((result) => !result.success);

        console.log(
          `🗑️ Deletion summary: ${successfulDeletions.length} successful, ${failedDeletions.length} failed`,
        );

        if (failedDeletions.length > 0) {
          console.warn('⚠️ Some options could not be deleted:', failedDeletions);
        }

        // Clear the target options array to ensure clean state
        updatedTarget.options = [];
      }

      // Wait a brief moment to ensure deletions are processed
      await new Promise((resolve) => setTimeout(resolve, 100));

      // Then create new options based on source (preserving exact count and optionText)
      console.log(`🔘 Creating ${source.options.length} new options based on source...`);

      for (let i = 0; i < source.options.length; i++) {
        const sourceOption = source.options[i];

        // Skip if sourceOption is undefined (safety check)
        if (!sourceOption) {
          console.warn(`⚠️ Source option at index ${i} is undefined, skipping...`);
          continue;
        }

        try {
          const newOptionData = {
            optionText: sourceOption.optionText || '', // ✅ Preserve original optionText
            value: sourceOption.value || 0,
            sequence: sourceOption.sequence || i + 1, // Ensure proper sequence
            itemBlockId: target.id,
            ...(sourceOption.imagePath && { imagePath: sourceOption.imagePath }),
            ...(sourceOption.nextSection && { nextSection: sourceOption.nextSection }),
          };

          console.log(
            `🔘 Creating option ${i + 1}/${source.options.length} with data:`,
            newOptionData,
          );
          const createdOption = await optionService.createOption(newOptionData);

          if (createdOption) {
            createdOptions.push(createdOption);
            console.log(`✅ Option ${i + 1} created successfully:`, {
              id: createdOption.id, // ✅ Backend-generated ID
              optionText: createdOption.optionText,
              value: createdOption.value,
              sequence: createdOption.sequence,
            });
          }
        } catch (error) {
          console.error(`❌ Failed to create option ${i + 1}:`, error);
          // Continue with other options even if one fails
        }
      }

      // Verify we created the exact number of options as the source
      if (createdOptions.length === source.options.length) {
        updatedTarget.options = createdOptions;
        console.log(
          `✅ Successfully created exactly ${createdOptions.length} options matching source count`,
          {
            expectedCount: source.options.length,
            actualCount: createdOptions.length,
            createdOptionsData: createdOptions.map((opt) => ({
              id: opt.id, // ✅ These are real backend-generated IDs
              optionText: opt.optionText,
              value: opt.value,
              sequence: opt.sequence,
            })),
          },
        );
      } else {
        console.error(
          `❌ Option count mismatch! Expected: ${source.options.length}, Created: ${createdOptions.length}`,
        );
        // Still update with what we have, but log the discrepancy
        updatedTarget.options = createdOptions;
      }

      // Final safety check: Ensure no duplicate or unexpected options exist
      if (updatedTarget.options && updatedTarget.options.length > 0) {
        const uniqueIds = new Set(updatedTarget.options.map((opt) => opt.id));
        if (uniqueIds.size !== updatedTarget.options.length) {
          console.error('❌ Duplicate option IDs detected in duplicated block!', {
            totalOptions: updatedTarget.options.length,
            uniqueIds: uniqueIds.size,
            optionIds: updatedTarget.options.map((opt) => opt.id),
          });
        }
      }
    }

    console.log('✅ Content copy process with backend persistence completed successfully', {
      finalOptionsCount: updatedTarget.options?.length || 0,
      finalQuestionsCount: updatedTarget.questions?.length || 0,
    });

    return updatedTarget;
  } catch (error) {
    console.error('❌ Error during content copy process with backend persistence:', error);
    // Return original target if copy fails
    return target;
  }
};
